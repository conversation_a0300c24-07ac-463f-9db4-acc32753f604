package com.snct.typhoon.mapper;

import com.snct.typhoon.domain.TyphoonPoint;

import java.util.List;

/**
 * 台风路径点位 数据层
 *
 * <AUTHOR>
 */
public interface TyphoonPointMapper {

    /**
     * 查询台风登陆地址
     *
     * @param typhoonPoint 台风登陆地址
     * @return 台风信息
     */
    public TyphoonPoint selectTyphoonPoint(TyphoonPoint typhoonPoint);

    /**
     * 查询台风登陆地址
     *
     * @param typhoonPoint 台风登陆地址
     * @return 台风集合
     */
    public List<TyphoonPoint> selectTyphoonPointList(TyphoonPoint typhoonPoint);

    /**
     * 新增台风登陆地址
     *
     * @param typhoonPoint 台风登陆地址
     * @return 结果
     */
    public int addTyphoonPoint(TyphoonPoint typhoonPoint);

    /**
     * 修改台风登陆地址
     *
     * @param typhoonPoint 台风登陆地址
     * @return 结果
     */
    public int updateTyphoonPoint(TyphoonPoint typhoonPoint);

}