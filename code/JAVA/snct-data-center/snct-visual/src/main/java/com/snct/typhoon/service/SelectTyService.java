package com.snct.typhoon.service;


import com.snct.typhoon.domain.Typhoon;
import com.snct.typhoon.domain.TyphoonInfoVo;

import java.util.List;

/**
 * @description: 获取台风数据
 * @author: rr
 * @create: 2020-10-27 17:12
 **/
public interface SelectTyService {
    /**
     * 查询台风列表
     *
     * @param yearsTime 年份
     * @return 航次集合
     */
    public List<Typhoon> selectTyphoonList(Integer yearsTime);


    public List<TyphoonInfoVo> selectTyphoonPoint(Integer tfId);
}
