package com.snct.typhoon.service.impl;


import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.typhoon.domain.Typhoon;
import com.snct.typhoon.domain.TyphoonInfoVo;
import com.snct.typhoon.service.SelectTyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: rr
 * @create: 2020-10-27 17:15
 **/
@Service
public class SelectTyServiceImpl implements SelectTyService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public List<Typhoon> selectTyphoonList(Integer yearsTime) {
        ValueOperations<String, List<Typhoon>> opsForValue1 = redisTemplate.opsForValue();
        List<Typhoon> typhoonList = opsForValue1.get(RedisParameter.TYPHOON_LIST + yearsTime);
            return typhoonList;
        }


    @Override
    public List<TyphoonInfoVo> selectTyphoonPoint(Integer tfId) {
        ValueOperations<String, List<TyphoonInfoVo>> opsForValue1 = redisTemplate.opsForValue();
        List<TyphoonInfoVo> TyphoonInfoVoList = opsForValue1.get(RedisParameter.TYPHOON_INFO + tfId);
        return TyphoonInfoVoList;
    }



}
