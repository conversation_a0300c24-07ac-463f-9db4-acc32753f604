package com.snct.typhoon.mapper;



import com.snct.typhoon.domain.TyphoonForecastPoint;

import java.util.List;

/**
 * 台风路径点位 数据层
 *
 * <AUTHOR>
 */
public interface TyphoonForecastPointMapper {


    /**
     * 查询台风路径预测点位
     *
     * @param typhoonForecastPoint 台风路径预测点位
     * @return 台风信息
     */
    public TyphoonForecastPoint selectTyphoonForecastPoint(TyphoonForecastPoint typhoonForecastPoint);

    /**
     * 查询台风路径预测点位
     *
     * @param typhoonForecastPoint 台风路径预测点位
     * @return 台风集合
     */
    public List<TyphoonForecastPoint> selectTyphoonForecastPointList(TyphoonForecastPoint typhoonForecastPoint);

    /**
     * 新增台风路径预测点位
     *
     * @param typhoonForecastPoint 台风路径预测点位
     * @return 结果
     */
    public int addTyphoonForecastPoint(TyphoonForecastPoint typhoonForecastPoint);

    /**
     * 修改台风路径预测点位
     *
     * @param typhoonForecastPoint 台风路径预测点位
     * @return 结果
     */
    public int updateTyphoonForecastPoint(TyphoonForecastPoint typhoonForecastPoint);

}